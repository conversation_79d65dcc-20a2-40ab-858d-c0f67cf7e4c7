<odoo>
  <data>

    <record id="view_rabbitmq_server_form" model="ir.ui.view">
      <field name="name">rabbitmq.server.form</field>
      <field name="model">rabbitmq.server</field>
      <field name="arch" type="xml">
        <form string="RabbitMQ Server">
          <header>
            <button name="action_test_connection" string="Test Connection" type="object" class="oe_highlight"/>
            <button name="action_publish_test_message" string="Publish Test Message" type="object" class="btn-primary"/>
            <button name="action_consume_messages" string="Consume Messages" type="object" class="btn-secondary"/>
          </header>
          <sheet>
            <div class="oe_title">
              <h1>
                <field name="name" placeholder="Service Name"/>
              </h1>
            </div>
            <div class="oe_button_box" name="button_box">
              <field name="connection_status" widget="badge" decoration-success="connection_status == 'connected'"
                decoration-danger="connection_status == 'error'"
                decoration-info="connection_status == 'disconnected'" />
            </div>
            <notebook>
              <page string="Connection Settings">
                <group>
                  <group string="Connection">
                    <field name="url" placeholder="amqp://user:pass@hostname:5672/vhost"/>
                    <field name="host" attrs="{'invisible': [('url', '!=', False)]}"/>
                    <field name="port" attrs="{'invisible': [('url', '!=', False)]}"/>
                    <field name="user" attrs="{'invisible': [('url', '!=', False)]}"/>
                    <field name="password" password="True" attrs="{'invisible': [('url', '!=', False)]}"/>
                    <field name="vhost" attrs="{'invisible': [('url', '!=', False)]}"/>
                  </group>
                  <group string="Queue">
                    <field name="queue_name"/>
                  </group>
                </group>
              </page>
              <page string="Status" name="status">
                <group>
                  <field name="connection_status" widget="badge" decoration-success="connection_status == 'connected'"
                    decoration-danger="connection_status == 'error'"
                    decoration-info="connection_status == 'disconnected'"/>
                  <field name="connection_error" attrs="{'invisible': [('connection_error', '=', False)]}"/>
                </group>
              </page>
            </notebook>
          </sheet>
        </form>
      </field>
    </record>

    <!-- Tree view -->
    <record id="view_rabbitmq_server_tree" model="ir.ui.view">
      <field name="name">rabbitmq.server.tree</field>
      <field name="model">rabbitmq.server</field>
      <field name="arch" type="xml">
        <tree string="RabbitMQ Servers" decoration-success="connection_status == 'connected'"
          decoration-danger="connection_status == 'error'" decoration-muted="connection_status == 'disconnected'">
          <field name="name"/>
          <field name="host"/>
          <field name="queue_name"/>
          <field name="connection_status"/>
          <field name="last_connection_check"/>
          <field name="consumer_running"/>
        </tree>
      </field>
    </record>



    <!-- Kanban view for dashboard -->
    <record id="view_rabbitmq_server_kanban" model="ir.ui.view">
      <field name="name">rabbitmq.server.kanban</field>
      <field name="model">rabbitmq.server</field>
      <field name="arch" type="xml">
        <kanban>
          <field name="id"/>
          <field name="name"/>
          <field name="host"/>
          <field name="port"/>
          <field name="queue_name"/>
          <field name="connection_status"/>
          <field name="last_connection_check"/>
          <field name="consumer_running"/>
          <templates>
            <t t-name="kanban-box">
              <div t-attf-class="oe_kanban_global_click o_kanban_record_has_image_fill">
                <div class="oe_kanban_details">
                  <div class="o_kanban_record_top mb-0">
                    <div class="o_kanban_record_headings">
                      <strong class="o_kanban_record_title">
                        <span>
                          <field name="name"/>
                        </span>
                      </strong>
                    </div>
                    <div class="o_dropdown_kanban dropdown">
                      <a role="button" class="dropdown-toggle o-no-caret btn" data-toggle="dropdown" href="#"
                        aria-label="Dropdown menu" title="Dropdown menu">
                        <span class="fa fa-ellipsis-v"/>
                      </a>
                      <div class="dropdown-menu" role="menu">
                        <a role="menuitem" type="object" name="action_run" class="dropdown-item">Start Consumer</a>
                        <a role="menuitem" type="object" name="action_reset_consumer_status" class="dropdown-item">Reset Status</a>
                      </div>
                    </div>
                  </div>
                  <div>
                    <t t-if="record.connection_status.raw_value == 'connected'">
                      <span class="badge badge-success">Connected</span>
                    </t>
                    <t t-elif="record.connection_status.raw_value == 'error'">
                      <span class="badge badge-danger">Error</span>
                    </t>
                    <t t-else="">
                      <span class="badge badge-secondary">Disconnected</span>
                    </t>
                    <span t-if="record.consumer_running.raw_value" class="badge badge-info">Consumer Running</span>
                  </div>
                  <div class="o_kanban_record_bottom">
                    <div class="oe_kanban_bottom_left">
                      <small><field name="host"/>:<field name="port"/></small>
                    </div>
                    <div class="oe_kanban_bottom_right">
                      <small>
                        <field name="last_connection_check" widget="relative_time"/>
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </t>
          </templates>
        </kanban>
      </field>
    </record>

    <!-- <record id="rabbitmq_form_view" model="ir.ui.view">
      <field name="name">RabbitMQ form</field>
      <field name="model">rabbitmq.server</field>
      <field name="arch" type="xml">
        <form string="Server Action">
          <header>    
            <button name="action_run" type="object" string="Run" class="btn-primary" />
          </header>

          <sheet>
            <div class="oe_title">
              <label for="name" class="oe_edit_only"/>
              <h1>
                <field name="name" placeholder=""/>
              </h1>
            </div>
            <group name="action_wrapper">
              <group>
                <field name="host"/>
                <field name="user"/>
                <field name="password" password="1"/>
                <field name="port"/>
                <field name="url"/>
              </group>
            </group>
          </sheet>
        </form>
      </field>
    </record>

    <record id="rabbitmq_tree_view" model="ir.ui.view">
      <field name="name">RabbitMQ tree</field>
      <field name="model">rabbitmq.server</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="host"/>
        </tree>
      </field>
    </record> -->

    <!-- <record id="action_rabbitmq" model="ir.actions.act_window">
      <field name="name">RabbitMQ</field>
      <field name="type">ir.actions.act_window</field>
      <field name="res_model">rabbitmq.server</field>
      <field name="view_mode">tree,form</field>
    </record> -->

    <record id="action_rabbitmq_server" model="ir.actions.act_window">
      <field name="name">RabbitMQ Servers</field>
      <field name="res_model">rabbitmq.server</field>
      <field name="view_mode">kanban,tree,form</field>
    </record>

    <menuitem id="menu_rabbit" name="RabbitMQ" parent="base.menu_custom"/>
    <menuitem id="menu_rabbit_mq" name="RabbitMQ Server" action="action_rabbitmq_server" parent="menu_rabbit"/>
  </data>
</odoo>