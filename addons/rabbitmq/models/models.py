import os
import time
from odoo import api, fields, models, registry, SUPERUSER_ID
import kombu
import logging
import socket
import json
import threading
import time
from datetime import datetime
import psycopg2

_logger = logging.getLogger(__name__)

RABBITMQ_CONNECTION_STOP_EVENTS = {}
class RabbitmqServer(models.Model):
    _name = "rabbitmq.server"
    _description = "RabbitMQ Server Configuration (Kombu Version)"

    url = fields.Char("MQ Single URL",)
    name = fields.Char("Service Name", required=True, default="RabbitMQ Service")
    host = fields.Char("Server Address", required=True)
    user = fields.Char("Username", required=True)
    password = fields.Char("Password", required=True)
    port = fields.Integer("Port", required=True, default=5672)
    vhost = fields.Char("Virtual Host", default="/")
    queue_name = fields.Char("Default Queue", default="MQ_CLIMAS_ODOO_ROUTE_KEY")
    auto_start = fields.<PERSON><PERSON>an("Auto-Start Consumer", default=True)
    
    # New fields for connection status
    connection_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string="Connection Status", default='disconnected', readonly=True)
    last_connection_check = fields.Datetime("Last Connection Check")
    connection_error = fields.Text("Connection Error", readonly=True)
    consumer_running = fields.Boolean("Consumer Running", default=False, readonly=True)
    last_consumer_start = fields.Datetime("Last Consumer Start", readonly=True)
    consumer_pid = fields.Integer("Consumer Process ID", readonly=True)



    def _safe_write(self, values):
        """Simple safe write with retry for concurrent updates"""
        for attempt in range(3):
            try:
                self.write(values)
                return True
            except psycopg2.OperationalError as e:
                if "could not serialize access due to concurrent update" in str(e):
                    if attempt < 2:
                        time.sleep(0.1)
                        continue
                    _logger.warning(f"Concurrent update failed after retries: {e}")
                    return False
                raise
            except Exception as e:
                _logger.error(f"Write error: {e}")
                return False
        return False

    def _is_consumer_process_running(self):
        """Check if the consumer process is actually running"""
        if not self.consumer_running or not self.consumer_pid:
            return False

        try:
            
            # Check if process exists
            os.kill(self.consumer_pid, 0)
            _logger.info(f"Consumer process {self.consumer_pid} is running")
            return True
        except (OSError, ProcessLookupError):
            # Process doesn't exist
            _logger.warning(f"Consumer process {self.consumer_pid} is not running")
            return False

    def _is_consumer_actually_running(self):
        """Check if consumer is actually running - Docker container aware"""
        # In Docker scaling scenarios, we can't rely on in-memory stop events
        # Instead, rely on database state and process validation

        if not self.consumer_running:
            return False

        # If we have a PID, check if the process exists
        if self.consumer_pid:
            return self._is_consumer_process_running()

        # If no PID but marked as running, assume it's running in another container
        # This prevents multiple containers from starting consumers
        return True

    def _detect_and_cleanup_zombie_thread(self):
        """Detect and cleanup zombie threads that died without proper cleanup"""
        if not self.consumer_running:
            return False

        # If database says running but thread/process is dead, it's a zombie
        if not self._is_consumer_actually_running():
            _logger.warning(f"Detected zombie consumer for server {self.id} - cleaning up")

            # Clean up stop event if exists
            stop_key = f"stop_{self.id}"
            if stop_key in RABBITMQ_CONNECTION_STOP_EVENTS:
                del RABBITMQ_CONNECTION_STOP_EVENTS[stop_key]

            # Reset database status
            self._safe_write({
                'consumer_running': False,
                'consumer_pid': False,
                'connection_status': 'disconnected'
            })
            return True
        return False

    def _stop_consumer_gracefully(self):
        """Gracefully stop the consumer thread using stop event"""
        if not self.consumer_running:
            _logger.info(f"Consumer for server {self.id} is not running")
            return True

        stop_key = f"stop_{self.id}"
        if stop_key in RABBITMQ_CONNECTION_STOP_EVENTS:
            _logger.info(f"Signaling consumer {self.id} to stop gracefully")
            RABBITMQ_CONNECTION_STOP_EVENTS[stop_key].set()

            # Wait a bit for graceful shutdown
            
            time.sleep(2)

            # Check if it stopped gracefully
            if not self._is_consumer_actually_running():
                _logger.info(f"Consumer {self.id} stopped gracefully")
                return True
            else:
                _logger.warning(f"Consumer {self.id} did not stop gracefully")
                return False
        else:
            _logger.warning(f"No stop event found for consumer {self.id}")
            return False

    def get_thread_info(self):
        """Get information about all running threads for debugging"""
        thread_info = []
        for thread in threading.enumerate():
            thread_info.append({
                'name': thread.name,
                'alive': thread.is_alive(),
                'daemon': thread.daemon,
                'ident': thread.ident
            })
        return thread_info

    def action_show_thread_info(self):
        """Show thread information and stop events for debugging"""
        self.ensure_one()
        thread_info = self.get_thread_info()

        message = "Current Threads:\n"
        for info in thread_info:
            status = "✓" if info['alive'] else "✗"
            message += f"{status} {info['name']} (ID: {info['ident']})\n"

        message += f"\nActive Stop Events:\n"
        for key, event in RABBITMQ_CONNECTION_STOP_EVENTS.items():
            status = "🛑" if event.is_set() else "🟢"
            message += f"{status} {key}\n"

        if not RABBITMQ_CONNECTION_STOP_EVENTS:
            message += "No active stop events\n"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Thread & Stop Event Information',
                'message': message,
                'sticky': True,
                'type': 'info',
            }
        }

    def get_connection(self):
        """Create Kombu connection with Odoo-safe parameters"""
        if self.url:
            return kombu.Connection(self.url)
        else:
            return kombu.Connection(
                hostname=self.host,
                port=self.port,
                userid=self.user,
                password=self.password,
                virtual_host=self.vhost,
                ssl=False
            )


    def publish_message(self, body, queue_name=None, exchange=""):
        """Publish message using Kombu"""
        queue_name = queue_name or self.queue_name
        try:
            with self.get_connection() as conn:
                producer = conn.Producer()
                queue = kombu.Queue(
                    name=queue_name,
                    durable=True,
                    channel=conn.default_channel
                )
                queue.declare()
                producer.publish(
                    body,
                    exchange=exchange,
                    routing_key=queue_name,
                    serializer='json'
                )
        except Exception as e:
            _logger.error(f"Publish error: {e}")
            
        
        
    def _message_handler(self, body, message):
        """Event handler with dynamic method routing"""
        dbname = self.env.cr.dbname
        
        try:
            with registry(dbname).cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                
                try:
                    # Parse message content
                    msg_content = json.loads(body) if isinstance(body, str) else body
                    event = msg_content.get('event')
                    body_data = msg_content.get('body', {})
                    
                    _logger.info(f"Processing event: {event}")
                    
                    # Event handlers mapping
                    event_handlers = {
                        'DILEMMA_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_dillemas'
                        },
                        'AGENDA_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_agendas'
                        },
                        'PDF_REFERENCE_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_dilemma_reference'
                        },
                        'FAILED_RESULT': {
                            'model': 'cep.ag.project',
                            'method': 'save_failed_message'
                        },
                        'KEYWORD_FREQUENCY_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_frequency_analysis'
                        },
                        'SENTIMENT_ANALYSIS_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_sentiment_analysis'
                        },
                        'NGRAM_RESULT': {
                            'model': 'cep.ag.result',
                            'method': 'save_n_gram'
                        },
                        
                        'RECOMMENDATIONS_RESULT': {
                            'model': 'cep_rmt.recommendation_data',
                            'method': 'save_suggestions'
                        }
                    }
                    

                    if event in event_handlers:
                        handler = event_handlers[event]
                        model = env[handler['model']]
                        
                        if hasattr(model, handler['method']):
                            method = getattr(model, handler['method'])
                            method(body=body_data)
                            cr.commit()  # Commit after successful processing
                            _logger.info(f"Successfully processed {event}")
                        else:
                            _logger.error(f"Method {handler['method']} not found in {handler['model']}")
                            raise Exception(f"Handler method not found: {handler['method']}")
                    else:
                        _logger.warning(f"No handler for event: {event}")
                        raise Exception(f"Unknown event type: {event}")
                        
                except json.JSONDecodeError as e:
                    _logger.error(f"Invalid JSON in message: {str(e)}")
                    raise
                except Exception as e:
                    _logger.error(f"Error processing message: {str(e)}")
                    cr.rollback()  # Rollback on error
                    raise
                    
        except Exception as e:
            _logger.error(f"Database error in message handler: {str(e)}")
            raise

    def _start_consumer(self):
        """Start consumer thread with graceful shutdown support"""
        # Detect and cleanup any zombie threads first
        self._detect_and_cleanup_zombie_thread()

        # Check if consumer is actually running (both process and thread)
        if self.consumer_running and self._is_consumer_actually_running():
            _logger.info(f"Consumer already running for server {self.id}")
            return

        # Try graceful stop first if consumer is marked as running
        if self.consumer_running:
            if not self._stop_consumer_gracefully():
                # Force reset if graceful stop failed
                self._safe_write({
                    'consumer_running': False,
                    'consumer_pid': False,
                    'connection_status': 'disconnected'
                })

        # Store server info for thread (avoid self reference in thread)
        server_id = self.id
        dbname = self.env.cr.dbname

        def graceful_consumer():
            stop_key = f"stop_{server_id}"

            # Check if another consumer is already running before proceeding
            if stop_key in RABBITMQ_CONNECTION_STOP_EVENTS:
                _logger.warning(f"Consumer thread already exists for server {server_id}, aborting")
                return

            # Create stop event for graceful shutdown
            stop_event = threading.Event()
            RABBITMQ_CONNECTION_STOP_EVENTS[stop_key] = stop_event

            conn = None

            try:
                # Use database lock to prevent race conditions across Docker containers
                with registry(dbname).cursor() as cr:
                    env = api.Environment(cr, SUPERUSER_ID, {})

                    # Lock the specific server record to prevent concurrent consumer creation
                    cr.execute(
                        "SELECT id, consumer_running, consumer_pid FROM rabbitmq_server WHERE id = %s FOR UPDATE",
                        (server_id,)
                    )
                    result = cr.fetchone()

                    if not result:
                        _logger.error(f"Server {server_id} not found in database")
                        if stop_key in RABBITMQ_CONNECTION_STOP_EVENTS:
                            del RABBITMQ_CONNECTION_STOP_EVENTS[stop_key]
                        return

                    server = env['rabbitmq.server'].browse(server_id)

                    # Check if consumer is already marked as running by another container
                    if server.consumer_running:
                        _logger.warning(f"Consumer already running for server {server_id} (possibly in another container), aborting thread")
                        # Clean up our stop event since we're not proceeding
                        if stop_key in RABBITMQ_CONNECTION_STOP_EVENTS:
                            del RABBITMQ_CONNECTION_STOP_EVENTS[stop_key]
                        return

                    # Mark as running with PID and container info
                    server.write({
                        'consumer_running': True,
                        'consumer_pid': os.getpid(),
                        'last_consumer_start': fields.Datetime.now()
                    })
                    cr.commit()
                    _logger.info(f"Successfully marked consumer as running for server {server_id}")

                # Get server details and create connection
                with registry(dbname).cursor() as cr:
                    env = api.Environment(cr, SUPERUSER_ID, {})
                    server = env['rabbitmq.server'].browse(server_id)
                    conn = server.get_connection()
                    queue_name = server.queue_name
                    host = server.host
                    port = server.port

                conn.connect()
                _logger.info(f"Consumer connected to {host}:{port}")

                # Update connection status after successful connection
                with registry(dbname).cursor() as cr:
                    env = api.Environment(cr, SUPERUSER_ID, {})
                    server = env['rabbitmq.server'].browse(server_id)
                    server.write({
                        'connection_status': 'connected',
                        'last_connection_check': fields.Datetime.now(),
                        'connection_error': False
                    })
                    cr.commit()

                # Setup queue with proper channel binding
                queue = kombu.Queue(queue_name, durable=True, channel=conn.default_channel)
                queue.declare()

                # Simple message handler - consumer cancellation prevents new messages during shutdown
                def handle_message(body, message):
                    try:
                        with registry(dbname).cursor() as cr:
                            env = api.Environment(cr, SUPERUSER_ID, {})
                            server = env['rabbitmq.server'].browse(server_id)
                            server._message_handler(body, message)
                        message.ack()
                    except Exception as e:
                        _logger.error(f"Message error: {e}")
                        message.reject(requeue=True)

                # Start consuming with stop event monitoring
                consumer = conn.Consumer(queue, callbacks=[handle_message])
                consumer.consume()
                _logger.info(f"🚀 Consumer started on {queue_name}")


                last_status_update = time.time()

                while not stop_event.is_set():
                    try:
                        # Use shorter timeout to check stop event more frequently
                        conn.drain_events(timeout=0.5)

                        # Update status every 30 seconds
                        current_time = time.time()
                        if current_time - last_status_update > 30:
                            try:
                                with registry(dbname).cursor() as cr:
                                    env = api.Environment(cr, SUPERUSER_ID, {})
                                    server = env['rabbitmq.server'].browse(server_id)
                                    server.write({
                                        'connection_status': 'connected',
                                        'last_connection_check': fields.Datetime.now()
                                    })
                                    cr.commit()
                                last_status_update = current_time
                            except Exception as status_error:
                                _logger.warning(f"Failed to update status: {status_error}")

                    except socket.timeout:
                        # Normal timeout, check stop event and continue
                        continue
                    except Exception as e:
                        _logger.error(f"Consumer error: {e}")
                        # Update error status using fresh cursor
                        try:
                            with registry(dbname).cursor() as cr:
                                env = api.Environment(cr, SUPERUSER_ID, {})
                                server = env['rabbitmq.server'].browse(server_id)
                                server.write({
                                    'consumer_running': False,
                                    'consumer_pid': False,
                                    'connection_status': 'error',
                                    'connection_error': str(e)
                                })
                                cr.commit()
                        except Exception as cleanup_error:
                            _logger.warning(f"Failed to update error status: {cleanup_error}")
                        break

                if stop_event.is_set():
                    _logger.info(f"Consumer {server_id} stopping gracefully")

            except Exception as e:
                _logger.error(f"Consumer failed: {e}")
            finally:
                # Cleanup stop event
                if stop_key in RABBITMQ_CONNECTION_STOP_EVENTS:
                    del RABBITMQ_CONNECTION_STOP_EVENTS[stop_key]
                    _logger.info(f"Removed stop event {stop_key}")

                # Close connection if it exists
                if conn:
                    try:
                        conn.close()
                    except Exception as close_error:
                        _logger.warning(f"Error closing connection: {close_error}")
                    

                # Cleanup database status using fresh cursor
                try:
                    with registry(dbname).cursor() as cr:
                        env = api.Environment(cr, SUPERUSER_ID, {})
                        server = env['rabbitmq.server'].browse(server_id)
                        server.write({
                            'consumer_running': False,
                            'consumer_pid': False,
                            'connection_status': 'disconnected'
                        })
                        cr.commit()
                except Exception as cleanup_error:
                    _logger.warning(f"Failed to cleanup consumer state: {cleanup_error}")

        if self.auto_start:
            # Final check to prevent race conditions - refresh record and check again
            self.refresh()
            if self.consumer_running and self._is_consumer_actually_running():
                _logger.info(f"Consumer already started by another process for server {self.id}")
                return True

            thread_name = f"RabbitMQ-Consumer-{self.id}"
            thread = threading.Thread(target=graceful_consumer, daemon=True, name=thread_name)
            thread.start()
            return True
        
        
    def action_run(self):
        """Test RabbitMQ connection and start consumer"""
        connection = self._start_consumer()
        if connection:   
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'RabbitMQ Consumer',
                    'message': 'Consumer started in background',
                    'sticky': False,
                    'type': 'success' if self.connection_status == 'connected' else 'warning',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'RabbitMQ Consumer',
                    'message': 'Failed to start consumer',
                    'sticky': False,
                    'type': 'danger',
                }
            }
        
        
    @api.model
    def _cron_check_rabbitmq_connections(self):
        """Cron job to periodically check all RabbitMQ connections and restart consumers if needed"""
        servers = self.search([])
        container_id = os.getpid()  # Use PID as container identifier
        _logger.info(f"Container {container_id}: Checking RabbitMQ connections for {len(servers)} servers")

        for server in servers:
            try:
                # Use database-level locking to prevent race conditions between multiple cron executions
                # This works across Docker containers since they share the same database
                self.env.cr.execute(
                    "SELECT id FROM rabbitmq_server WHERE id = %s FOR UPDATE NOWAIT",
                    (server.id,)
                )

                # Refresh server record after acquiring lock
                server = self.browse(server.id)

                _logger.info(f"Container {container_id}: Processing server {server.name} (ID: {server.id})")
                _logger.info(f"Container {container_id}: Server {server.id} - consumer_running: {server.consumer_running}, consumer_pid: {server.consumer_pid}")

                # First, detect and cleanup any zombie threads
                was_zombie = server._detect_and_cleanup_zombie_thread()

                # Check if consumer is actually running (database-based check for Docker compatibility)
                is_actually_running = server._is_consumer_actually_running()

                _logger.info(f"Container {container_id}: Server {server.id} - is_actually_running: {is_actually_running}, was_zombie: {was_zombie}")

                # Only restart if auto_start is enabled and consumer is not actually running
                if server.auto_start and not is_actually_running:
                    if was_zombie:
                        _logger.info(f"Container {container_id}: Cleaned up zombie consumer for {server.name}, starting new consumer")
                    else:
                        _logger.info(f"Container {container_id}: No consumer running for {server.name}, starting new consumer")
                    server._start_consumer()
                elif is_actually_running:
                    _logger.info(f"Container {container_id}: RabbitMQ server {server.name} consumer is running properly")
                elif not server.auto_start:
                    _logger.debug(f"Container {container_id}: Auto-start disabled for {server.name}, skipping consumer check")

            except psycopg2.OperationalError as e:
                if "could not obtain lock" in str(e):
                    _logger.info(f"Container {container_id}: Another cron job is already processing server {server.name}, skipping")
                else:
                    _logger.error(f"Container {container_id}: Database error checking RabbitMQ connection for {server.name}: {e}")
            except Exception as e:
                _logger.error(f"Container {container_id}: Error cronjob checking RabbitMQ connection for {server.name}: {e}")

    def action_reset_consumer_status(self):
        """Manually reset consumer status - useful after server restarts"""
        self.ensure_one()

        self._safe_write({
            'consumer_running': False,
            'consumer_pid': False,
            'consumer_thread_name': False,
            'connection_status': 'disconnected'
        })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Consumer Status Reset',
                'message': 'Consumer status has been reset',
                'sticky': False,
                'type': 'success',
            }
        }

    def action_stop_consumer(self):
        """Gracefully stop the consumer"""
        self.ensure_one()

        if not self.consumer_running:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Consumer Stop',
                    'message': 'Consumer is not running',
                    'sticky': False,
                    'type': 'info',
                }
            }

        # Try graceful stop first
        success = self._stop_consumer_gracefully()

        if success:
            message = 'Consumer stopped gracefully'
            msg_type = 'success'
        else:
            # Force stop if graceful failed
            self._detect_and_cleanup_zombie_thread()
            message = 'Consumer was force stopped (graceful stop failed)'
            msg_type = 'warning'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Consumer Stop',
                'message': message,
                'sticky': False,
                'type': msg_type,
            }
        }

    def action_refresh_status(self):
        """Manually refresh consumer and connection status"""
        self.ensure_one()

        # Check actual status
        stop_key = f"stop_{self.id}"
        has_stop_event = stop_key in RABBITMQ_CONNECTION_STOP_EVENTS
        is_process_running = self._is_consumer_process_running()

        status_info = []
        status_info.append(f"Database Status: {'Running' if self.consumer_running else 'Stopped'}")
        status_info.append(f"Process Running: {'Yes' if is_process_running else 'No'}")
        status_info.append(f"Stop Event Exists: {'Yes' if has_stop_event else 'No'}")
        status_info.append(f"Connection Status: {self.connection_status}")

        if self.consumer_pid:
            status_info.append(f"Process ID: {self.consumer_pid}")

        message = "\n".join(status_info)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Consumer Status',
                'message': message,
                'sticky': True,
                'type': 'info',
            }
        }