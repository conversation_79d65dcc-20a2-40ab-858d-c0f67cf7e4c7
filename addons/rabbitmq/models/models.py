from odoo import api, fields, models
import kombu
import logging
import json

_logger = logging.getLogger(__name__)
class RabbitmqServer(models.Model):
    _name = "rabbitmq.server"
    _description = "RabbitMQ Server Configuration"

    url = fields.Char("MQ Single URL")
    name = fields.Char("Service Name", required=True, default="RabbitMQ Service")
    host = fields.Char("Server Address", required=True)
    user = fields.Char("Username", required=True)
    password = fields.Char("Password", required=True)
    port = fields.Integer("Port", required=True, default=5672)
    vhost = fields.Char("Virtual Host", default="/")
    queue_name = fields.Char("Default Queue", default="MQ_CLIMAS_ODOO_ROUTE_KEY")

    # Simple connection status
    connection_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string="Connection Status", default='disconnected', readonly=True)
    connection_error = fields.Text("Connection Error", readonly=True)

    def test_connection(self):
        """Test RabbitMQ connection"""
        try:
            with self.get_connection() as conn:
                conn.connect()
                self.write({
                    'connection_status': 'connected',
                    'connection_error': False
                })
                _logger.info(f"Successfully connected to RabbitMQ server {self.name}")
                return True
        except Exception as e:
            error_msg = str(e)
            self.write({
                'connection_status': 'error',
                'connection_error': error_msg
            })
            _logger.error(f"Failed to connect to RabbitMQ server {self.name}: {error_msg}")
            return False

    def get_connection(self):
        """Create Kombu connection"""
        if self.url:
            return kombu.Connection(self.url)
        else:
            return kombu.Connection(
                hostname=self.host,
                port=self.port,
                userid=self.user,
                password=self.password,
                virtual_host=self.vhost,
                ssl=False
            )

    def publish_message(self, body, queue_name=None, exchange=""):
        """Publish message using Kombu"""
        queue_name = queue_name or self.queue_name
        try:
            with self.get_connection() as conn:
                producer = conn.Producer()
                queue = kombu.Queue(
                    name=queue_name,
                    durable=True,
                    channel=conn.default_channel
                )
                queue.declare()
                producer.publish(
                    body,
                    exchange=exchange,
                    routing_key=queue_name,
                    serializer='json'
                )
                _logger.info(f"Message published to queue {queue_name}")
                return True
        except Exception as e:
            _logger.error(f"Publish error: {e}")
            return False

    def consume_messages(self, callback=None, timeout=1.0):
        """Simple message consumer - processes one message at a time"""
        try:
            with self.get_connection() as conn:
                queue = kombu.Queue(
                    name=self.queue_name,
                    durable=True,
                    channel=conn.default_channel
                )
                queue.declare()

                def default_callback(body, message):
                    _logger.info(f"Received message: {body}")
                    message.ack()

                handler = callback or default_callback
                consumer = conn.Consumer(queue, callbacks=[handler])
                consumer.consume()

                # Process one message
                conn.drain_events(timeout=timeout)
                return True

        except Exception as e:
            _logger.error(f"Consumer error: {e}")
            return False

    def action_test_connection(self):
        """Test RabbitMQ connection"""
        self.ensure_one()
        success = self.test_connection()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'RabbitMQ Connection Test',
                'message': 'Connection successful!' if success else f'Connection failed: {self.connection_error}',
                'sticky': False,
                'type': 'success' if success else 'danger',
            }
        }

    def action_publish_test_message(self):
        """Publish a test message"""
        self.ensure_one()
        test_message = {
            'event': 'TEST_MESSAGE',
            'body': {
                'message': 'Test message from Odoo',
                'timestamp': fields.Datetime.now().isoformat()
            }
        }

        success = self.publish_message(test_message)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Test Message',
                'message': 'Test message published successfully!' if success else 'Failed to publish test message',
                'sticky': False,
                'type': 'success' if success else 'danger',
            }
        }

    def action_consume_messages(self):
        """Consume messages manually"""
        self.ensure_one()

        def simple_handler(body, message):
            _logger.info(f"Received message: {body}")
            message.ack()

        success = self.consume_messages(callback=simple_handler, timeout=5.0)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Message Consumer',
                'message': 'Message consumption completed' if success else 'Failed to consume messages',
                'sticky': False,
                'type': 'success' if success else 'warning',
            }
        }