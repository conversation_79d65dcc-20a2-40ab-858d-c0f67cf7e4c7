from odoo import api, fields, models
import kombu
import logging
import json
import time
from kombu.exceptions import ConnectionError, ChannelError

_logger = logging.getLogger(__name__)
class RabbitmqServer(models.Model):
    _name = "rabbitmq.server"
    _description = "RabbitMQ Server Configuration"

    url = fields.Char("MQ Single URL")
    name = fields.Char("Service Name", required=True, default="RabbitMQ Service")
    host = fields.Char("Server Address", required=True)
    user = fields.Char("Username", required=True)
    password = fields.Char("Password", required=True)
    port = fields.Integer("Port", required=True, default=5672)
    vhost = fields.Char("Virtual Host", default="/")
    queue_name = fields.Char("Default Queue", default="MQ_CLIMAS_ODOO_ROUTE_KEY")

    # Simple connection status
    connection_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string="Connection Status", default='disconnected', readonly=True)
    connection_error = fields.Text("Connection Error", readonly=True)

    def test_connection(self):
        """Test RabbitMQ connection with retry logic"""
        def _test():
            with self.get_connection() as conn:
                conn.ensure_connection(max_retries=3)
                # Test queue operations to ensure full functionality
                test_queue = kombu.Queue(
                    name=f"test_{self.queue_name}",
                    durable=False,
                    auto_delete=True,
                    channel=conn.default_channel
                )
                test_queue.declare()
                test_queue.delete()
                return True

        try:
            result = self._retry_operation(_test)
            self.write({
                'connection_status': 'connected',
                'connection_error': False
            })
            _logger.info(f"Successfully connected to RabbitMQ server {self.name}")
            return result
        except Exception as e:
            error_msg = str(e)
            self.write({
                'connection_status': 'error',
                'connection_error': error_msg
            })
            _logger.error(f"Failed to connect to RabbitMQ server {self.name}: {error_msg}")
            return False

    def check_broker_health(self):
        """Comprehensive broker health check"""
        health_info = {
            'connection': False,
            'queue_operations': False,
            'publish_test': False,
            'overall_status': 'error'
        }

        try:
            # Test basic connection
            if self.test_connection():
                health_info['connection'] = True

                # Test publishing
                test_msg = {'health_check': True, 'timestamp': time.time()}
                if self.publish_message(test_msg, f"health_check_{self.queue_name}"):
                    health_info['publish_test'] = True
                    health_info['queue_operations'] = True
                    health_info['overall_status'] = 'healthy'

        except Exception as e:
            _logger.error(f"Health check failed for {self.name}: {e}")
            health_info['error'] = str(e)

        return health_info

    def get_connection(self):
        """Create Kombu connection with resilience settings"""
        connection_params = {
            'heartbeat': 30,  # Send heartbeat every 30 seconds
            'transport_options': {
                'max_retries': 3,
                'interval_start': 0.1,
                'interval_step': 0.2,
                'interval_max': 1.0,
            }
        }

        if self.url:
            return kombu.Connection(self.url, **connection_params)
        else:
            return kombu.Connection(
                hostname=self.host,
                port=self.port,
                userid=self.user,
                password=self.password,
                virtual_host=self.vhost,
                ssl=False,
                **connection_params
            )

    def _retry_operation(self, operation, max_retries=3, delay=1.0):
        """Retry an operation with exponential backoff"""
        for attempt in range(max_retries):
            try:
                return operation()
            except (ConnectionError, ChannelError, OSError) as e:
                if attempt == max_retries - 1:
                    raise e
                wait_time = delay * (2 ** attempt)
                _logger.warning(f"Operation failed (attempt {attempt + 1}/{max_retries}), retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
            except Exception as e:
                # Don't retry for non-connection errors
                raise e

    def publish_message(self, body, queue_name=None, exchange=""):
        """Publish message using Kombu with retry logic"""
        queue_name = queue_name or self.queue_name

        def _publish():
            with self.get_connection() as conn:
                conn.ensure_connection(max_retries=3)  # Ensure connection is alive
                producer = conn.Producer()
                queue = kombu.Queue(
                    name=queue_name,
                    durable=True,
                    channel=conn.default_channel
                )
                queue.declare()
                producer.publish(
                    body,
                    exchange=exchange,
                    routing_key=queue_name,
                    serializer='json'
                )
                return True

        try:
            result = self._retry_operation(_publish)
            _logger.info(f"Message published to queue {queue_name}")
            self.write({'connection_status': 'connected', 'connection_error': False})
            return result
        except Exception as e:
            error_msg = str(e)
            _logger.error(f"Publish error after retries: {error_msg}")
            self.write({'connection_status': 'error', 'connection_error': error_msg})
            return False

    def consume_messages(self, callback=None, timeout=1.0):
        """Simple message consumer with retry logic"""

        def _consume():
            with self.get_connection() as conn:
                conn.ensure_connection(max_retries=3)  # Ensure connection is alive
                queue = kombu.Queue(
                    name=self.queue_name,
                    durable=True,
                    channel=conn.default_channel
                )
                queue.declare()

                def default_callback(body, message):
                    _logger.info(f"Received message: {body}")
                    message.ack()

                handler = callback or default_callback
                consumer = conn.Consumer(queue, callbacks=[handler])
                consumer.consume()

                # Process one message
                conn.drain_events(timeout=timeout)
                return True

        try:
            result = self._retry_operation(_consume)
            self.write({'connection_status': 'connected', 'connection_error': False})
            return result
        except Exception as e:
            error_msg = str(e)
            _logger.error(f"Consumer error after retries: {error_msg}")
            self.write({'connection_status': 'error', 'connection_error': error_msg})
            return False

    def action_test_connection(self):
        """Test RabbitMQ connection"""
        self.ensure_one()
        success = self.test_connection()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'RabbitMQ Connection Test',
                'message': 'Connection successful!' if success else f'Connection failed: {self.connection_error}',
                'sticky': False,
                'type': 'success' if success else 'danger',
            }
        }

    def action_publish_test_message(self):
        """Publish a test message"""
        self.ensure_one()
        test_message = {
            'event': 'TEST_MESSAGE',
            'body': {
                'message': 'Test message from Odoo',
                'timestamp': fields.Datetime.now().isoformat()
            }
        }

        success = self.publish_message(test_message)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Test Message',
                'message': 'Test message published successfully!' if success else 'Failed to publish test message',
                'sticky': False,
                'type': 'success' if success else 'danger',
            }
        }

    def action_consume_messages(self):
        """Consume messages manually"""
        self.ensure_one()

        def simple_handler(body, message):
            _logger.info(f"Received message: {body}")
            message.ack()

        success = self.consume_messages(callback=simple_handler, timeout=5.0)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Message Consumer',
                'message': 'Message consumption completed' if success else 'Failed to consume messages',
                'sticky': False,
                'type': 'success' if success else 'warning',
            }
        }

    def action_health_check(self):
        """Comprehensive health check action"""
        self.ensure_one()
        health_info = self.check_broker_health()

        if health_info['overall_status'] == 'healthy':
            message = "✅ Broker is healthy!\n"
            message += f"• Connection: {'✓' if health_info['connection'] else '✗'}\n"
            message += f"• Queue Operations: {'✓' if health_info['queue_operations'] else '✗'}\n"
            message += f"• Publish Test: {'✓' if health_info['publish_test'] else '✗'}"
            msg_type = 'success'
        else:
            message = "❌ Broker health issues detected!\n"
            message += f"• Connection: {'✓' if health_info['connection'] else '✗'}\n"
            message += f"• Queue Operations: {'✓' if health_info['queue_operations'] else '✗'}\n"
            message += f"• Publish Test: {'✓' if health_info['publish_test'] else '✗'}"
            if 'error' in health_info:
                message += f"\nError: {health_info['error']}"
            msg_type = 'danger'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'RabbitMQ Health Check',
                'message': message,
                'sticky': True,
                'type': msg_type,
            }
        }