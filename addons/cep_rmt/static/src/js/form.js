// CEP RMT Form Controller - Simplified and Merged
$(document).ready(function () {
  'use strict';

  // Initialize global namespace
  window.CEP_RMT = window.CEP_RMT || {};
  window.CEP_RMT.Steps = window.CEP_RMT.Steps || {};

  // =============================================================================
  // FORM STATE MANAGEMENT
  // =============================================================================

  const formState = {
    currentStep: 1,
    totalSteps: 12,
    isLoading: false,
    recordId: null,
    data: {
      // Step 1 - Recommendation
      recommendation: '',
      selectedFile: null,
      fileName: '',
      outputCount: 3,

      // Step 2 - Stakeholders Involved
      stakeholders: '',
      selectedStakeholders: [],

      // Step 3 - Stakeholder Roles
      implementationStakeholder: '',
      ownershipStakeholder: '',
      progressTracker: '',
      feedbackHandler: '',
      outcomeEvaluator: '',

      // Step 4 - Governance Rules
      governance: '',
      selectedGovernance: [],

      // Step 5 - Challenges
      challenges: '',
      selectedChallenges: [],

      // Step 6 - Coordination
      coordination: '',
      selectedCoordination: [],

      // Step 7 - Team Setup
      grupMotor: 'yes',
      teamLeader: { name: '', phone: '', email: '' },
      teamMembers: {
        name: '',
        background: '',
        taskDescription: '',
        role: '',
        contact: '',
      },
      activityTimeline: {
        activityName: '',
        milestone: '',
        startDate: '',
        endDate: '',
        deliverables: '',
        responsiblePerson: '',
        budget: '',
      },

      // Step 8 - Implementation
      implementation: '',
      selectedImplementation: [],

      // Step 9 - Outcomes
      outcomes: '',
      selectedOutcomes: [],

      // Step 10 - Monitoring
      monitoring: '',
      selectedMonitoring: [],

      // Step 11 - Feedback
      feedback: '',
      selectedFeedback: [],

      // Step 12 - Final Details
      title: '',
      author: '',
      deliverableLink: '',
      supportingDocs: [],
      results: [],
    },
  };

  let resultsState;

  // =============================================================================
  // STEP CONFIGURATION
  // =============================================================================

  const STEP_CONFIG = {
    1: {
      name: 'recommendation',
      hasFileUpload: true,
      hasSuggestions: false,
      validation: () => {
        if (!formState.data.recommendation.trim()) {
          return 'Recommendation text is required';
        }
        if (!formState.data.selectedFile) {
          return 'NAP file is required';
        }
        return null;
      },
    },
    2: {
      name: 'stakeholders_involved',
      hasFileUpload: false,
      hasSuggestions: true,
      validation: () => {
        if (
          !formState.data.stakeholders.trim() &&
          !formState.data.selectedStakeholders.length
        ) {
          return 'Please enter stakeholders information';
        }
        return null;
      },
    },
    3: {
      name: 'stakeholders',
      hasFileUpload: false,
      hasSuggestions: false,
      validation: () => {
        if (
          !formState.data.implementationStakeholder.trim() &&
          !formState.data.ownershipStakeholder.trim()
        ) {
          return 'Implementation stakeholder is required';
        }
        return null;
      },
    },
    4: {
      name: 'governance_rules',
      hasFileUpload: false,
      hasSuggestions: true,

      validation: () => {
        if (
          !formState.data.governance.trim() &&
          !formState.data.selectedGovernance.length
        ) {
          return 'Please enter governance rules information';
        }
        return null;
      },
    },
    5: {
      name: 'identified_challenges',

      hasFileUpload: false,
      hasSuggestions: true,

      validation: () => {
        if (
          !formState.data.challenges.trim() &&
          !formState.data.selectedChallenges.length
        ) {
          return 'Please enter challenges information';
        }
        return null;
      },
    },
    6: {
      name: 'coordination_plan',
      hasFileUpload: false,
      hasSuggestions: true,
      validation: () => {
        if (
          !formState.data.coordination.trim() &&
          !formState.data.selectedCoordination.length
        ) {
          return 'Please enter coordination information';
        }
        return null;
      },
    },
    7: {
      name: 'team_info',
      hasFileUpload: false,
      hasSuggestions: false,
      validation: () => {
        
        if (formState.data.grupMotor === 'yes') {
          if (!formState.data.teamLeader.name.trim()) {
            return 'Team leader name is required';
          }
          if (!formState.data.teamLeader.email.trim()) {
            return 'Team leader email is required';
          }
          if (!formState.data.teamMembers.name.trim()) {
            return 'Team member information is required';
          }
          if (!formState.data.activityTimeline.activityName.trim()) {
            return 'Activity timeline information is required';
          }
        }
        return null;
      },
    },
    8: {
      name: 'implementation_steps',
      hasFileUpload: false,
      hasSuggestions: true,

      validation: () => {
        if (
          !formState.data.implementation.trim() &&
          !formState.data.selectedImplementation.length
        ) {
          return 'Please enter implementation information';
        }
        return null;
      },
    },
    9: {
      name: 'expected_outcomes',
      hasFileUpload: false,
      hasSuggestions: true,

      validation: () => {
        if (
          !formState.data.outcomes.trim() &&
          !formState.data.selectedOutcomes.length
        ) {
          return 'Please enter outcomes information';
        }
        return null;
      },
    },
    10: {
      name: 'monitoring_indicators',
      hasFileUpload: false,
      hasSuggestions: true,

      validation: () => {
        if (
          !formState.data.monitoring.trim() &&
          !formState.data.selectedMonitoring.length
        ) {
          return 'Please enter monitoring information';
        }
        return null;
      },
    },
    11: {
      name: 'feedback_mechanisms',
      hasFileUpload: false,
      hasSuggestions: true,

      validation: () => {
        if (
          !formState.data.feedback.trim() &&
          !formState.data.selectedFeedback.length
        ) {
          return 'Please enter feedback information';
        }
        return null;
      },
    },
    12: {
      name: 'final_details',
      hasFileUpload: true,
      hasSuggestions: false,
      validation: () => {
        if (!formState.data.title.trim()) {
          return 'Title is required';
        }
        if (!formState.data.author.trim()) {
          return 'Author information is required';
        }
        return null;
      },
    },
  };

  // =============================================================================
  // UTILITY FUNCTIONS
  // =============================================================================

  function showMessage(message, type = 'success') {
    const icon =
      type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    const alertClass = `alert-${type === 'success' ? 'success' : 'danger'}`;
    const timeout = type === 'success' ? 5000 : 10000;

    const alertHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        <i class="fa ${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;

    $('.alert').remove();
    $('#show-error-msg').prepend(alertHtml);

    setTimeout(() => $('.alert').fadeOut(), timeout);
  }

  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = (error) => reject(error);
    });
  }

  // =============================================================================
  // FILE UPLOAD HANDLING (STEP 1)
  // =============================================================================

  function initFileUpload() {
    // File input change
    $('#step1_file_upload')
      .off('change')
      .on('change', function () {
        const file = this.files[0];
        if (file) processFile(file);
      });

    // Drag and drop
    $('#drop-zone')
      .off('dragover dragleave drop click')
      .on('dragover', function (e) {
        e.preventDefault();
        e.stopPropagation();
        e.originalEvent.dataTransfer.dropEffect = 'copy';
        $(this).addClass('drag-over');
      })
      .on('dragleave', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('drag-over');
      })
      .on('drop', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('drag-over');
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) processFile(files[0]);
      })
      .on('click', function () {
        $('#step1_file_upload')[0].click();
      });

    // Remove file button
    $('#step1_remove_file_btn').off('click').on('click', removeFile);
  }

  function processFile(file) {
    // Validate file type
    if (!['application/pdf'].includes(file.type)) {
      showMessage('Please upload a PDF document', 'error');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      showMessage('File size must be less than 10MB', 'error');
      return;
    }

    // Store file data
    formState.data.selectedFile = file;
    formState.data.fileName = file.name;

    // Update UI
    $('#step1_file_name').text(file.name);
    $('#step1_file_info').removeClass('d-none');
    $('#drop-zone').addClass('d-none');
  }

  function removeFile() {
    // Clear file data
    formState.data.selectedFile = null;
    formState.data.fileName = '';

    // Reset UI
    $('#step1_file_upload').val('');
    $('#step1_file_info').addClass('d-none');
    $('#step1_file_name').text('');
    $('#drop-zone').removeClass('d-none');
  }

  // =============================================================================
  // INPUT FIELD HANDLERS
  // =============================================================================

  function bindInputEvents(stepNumber) {
    const inputMappings = {
      1: {
        '#step1_recommendation_input': 'recommendation',
        '#step1_output_count': 'outputCount',
      },
      2: {
        '#step2_stakeholders_input': 'stakeholders',
      },
      3: {
        '#step3_implementation_stakeholder': 'implementationStakeholder',
        '#step3_ownership_stakeholder': 'ownershipStakeholder',
        '#step3_progress_tracker': 'progressTracker',
        '#step3_feedback_handler': 'feedbackHandler',
        '#step3_outcome_evaluator': 'outcomeEvaluator',
      },
      4: {
        '#step4_governance_input': 'governance',
      },
      5: {
        '#step5_challenges_input': 'challenges',
      },
      6: {
        '#step6_coordination_input': 'coordination',
      },
      7: {
        '#step7_grup_motor_yes': 'grupMotor',
        '#step7_grup_motor_no': 'grupMotor',
        '#step7_team_leader_name': 'teamLeader.name',
        '#step7_team_leader_phone': 'teamLeader.phone',
        '#step7_team_leader_email': 'teamLeader.email',
        '#step7_team_member_name': 'teamMembers.name',
        '#step7_team_member_background': 'teamMembers.background',
        '#step7_team_member_task': 'teamMembers.taskDescription',
        '#step7_team_member_role': 'teamMembers.role',
        '#step7_team_member_contact': 'teamMembers.contact',
        '#step7_activity_name': 'activityTimeline.activityName',
        '#step7_activity_milestone': 'activityTimeline.milestone',
        '#step7_activity_start_date': 'activityTimeline.startDate',
        '#step7_activity_end_date': 'activityTimeline.endDate',
        '#step7_activity_deliverables': 'activityTimeline.deliverables',
        '#step7_activity_responsible': 'activityTimeline.responsiblePerson',
        '#step7_activity_budget': 'activityTimeline.budget',
      },
      8: {
        '#step8_implementation_input': 'implementation',
      },
      9: {
        '#step9_outcomes_input': 'outcomes',
      },
      10: {
        '#step10_monitoring_input': 'monitoring',
      },
      11: {
        '#step11_feedback_input': 'feedback',
      },
      12: {
        '#step12_title': 'title',
        '#step12_author': 'author',
        '#step12_deliverable_link': 'deliverableLink',
      },
    };

    const mappings = inputMappings[stepNumber];
    if (!mappings) return;

    Object.entries(mappings).forEach(([selector, dataKey]) => {
      $(selector)
        .off('input change')
        .on('input change', function () {
          const value =
            dataKey === 'outputCount' ? parseInt($(this).val()) : $(this).val();

          if (selector === '#step7_grup_motor_yes') {
            $('#team-members-section').show();
            $('#activity-timeline-section').show();
            $('#team-leader-section').show();
          } else if (selector === '#step7_grup_motor_no') {
            $('#team-members-section').hide();
            $('#activity-timeline-section').hide();
            $('#team-leader-section').hide();
          }

          // Handle nested properties (e.g., teamLeader.name)
          if (dataKey.includes('.')) {
            const [parent, child] = dataKey.split('.');
            formState.data[parent][child] = value;
          } else {
            formState.data[dataKey] = value;
          }
        });
    });
  }

  // =============================================================================
  // SUGGESTION HANDLING
  // =============================================================================

  function bindSuggestionEvents(stepNumber) {
    // Suggestion checkboxes
    $(document)
      .off('change', `input[id^="step${stepNumber}_suggestion"]`)
      .on('change', `input[id^="step${stepNumber}_suggestion"]`, function () {
        updateSelectedSuggestions(stepNumber);
      });

    // Reference link
    $(`#step${stepNumber}_reference_link`)
      .off('click')
      .on('click', function (e) {
        e.preventDefault();
        if (window.CEP_RMT.SuggestionsHandler) {
          window.CEP_RMT.SuggestionsHandler.showReferencesModal(stepNumber);
        }
      });
  }

  function updateSelectedSuggestions(stepNumber) {
    const selectedSuggestions = [];

    $(`input[id^="step${stepNumber}_suggestion"]:checked`).each(function () {
      const label = $(this).next('label').text().trim();
      selectedSuggestions.push(label);
    });

    // Map to correct data field
    const suggestionMappings = {
      2: 'selectedStakeholders',
      4: 'selectedGovernance',
      5: 'selectedChallenges',
      6: 'selectedCoordination',
      8: 'selectedImplementation',
      9: 'selectedOutcomes',
      10: 'selectedMonitoring',
      11: 'selectedFeedback',
    };

    const dataKey = suggestionMappings[stepNumber];
    if (dataKey) {
      formState.data[dataKey] = selectedSuggestions;
    }
  }

  // =============================================================================
  // STEP MANAGEMENT
  // =============================================================================

  function initStep(stepNumber) {
    const config = STEP_CONFIG[stepNumber];
    if (!config) return;

    // Bind input events
    bindInputEvents(stepNumber);

    // Handle file upload for steps 1 and 12
    if (config.hasFileUpload) {
      if (stepNumber === 1) {
        initFileUpload();
      } else if (stepNumber === 12) {
        initSupportingDocsUpload();
      }
    }

    // Handle suggestions
    if (config.hasSuggestions) {
      bindSuggestionEvents(stepNumber);
      if (window.CEP_RMT.SuggestionsHandler) {
        window.CEP_RMT.SuggestionsHandler.populateAutomatedSuggestions(
          stepNumber
        );
      }
    }
  }

  function validateStep(stepNumber) {
    const config = STEP_CONFIG[stepNumber];
    if (!config) return true;

    const errorMessage = config.validation();
    if (errorMessage) {
      showMessage(errorMessage, 'error');
      return false;
    }
    return true;
  }

  function collectStepData(stepNumber) {
    const dataMappings = {
      1: {
        recommendation_text: formState.data.recommendation,
        output_result_count: formState.data.outputCount,
        file_name: formState.data.fileName,
      },
      2: {
        stakeholders_text: formState.data.stakeholders,
        selected_stakeholders: formState.data.selectedStakeholders,
      },
      3: {
        implementation_stakeholder: formState.data.implementationStakeholder,
        ownership_stakeholder: formState.data.ownershipStakeholder,
        progress_tracker: formState.data.progressTracker,
        feedback_handler: formState.data.feedbackHandler,
        outcome_evaluator: formState.data.outcomeEvaluator,
      },
      4: {
        governance_text: formState.data.governance,
        selected_governance: formState.data.selectedGovernance,
      },
      5: {
        challenges_text: formState.data.challenges,
        selected_challenges: formState.data.selectedChallenges,
      },
      6: {
        coordination_text: formState.data.coordination,
        selected_coordination: formState.data.selectedCoordination,
      },
      7: {
        grup_motor: formState.data.grupMotor,
        team_leader: formState.data.teamLeader,
        team_members: formState.data.teamMembers,
        activity_timeline: formState.data.activityTimeline,
      },
      8: {
        implementation_text: formState.data.implementation,
        selected_implementation: formState.data.selectedImplementation,
      },
      9: {
        outcomes_text: formState.data.outcomes,
        selected_outcomes: formState.data.selectedOutcomes,
      },
      10: {
        monitoring_text: formState.data.monitoring,
        selected_monitoring: formState.data.selectedMonitoring,
      },
      11: {
        feedback_text: formState.data.feedback,
        selected_feedback: formState.data.selectedFeedback,
      },
      12: {
        title: formState.data.title,
        author: formState.data.author,
        deliverable_link: formState.data.deliverableLink,
        supporting_docs: formState.data.supportingDocs,
      },
    };

    return dataMappings[stepNumber] || {};
  }

  // =============================================================================
  // SUPPORTING DOCS UPLOAD FOR STEP 12
  // =============================================================================

  function initSupportingDocsUpload() {
    // File input change handler
    $('#step12_supporting_docs_upload')
      .off('change')
      .on('change', function (e) {
        const files = e.target.files;
        if (files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            processSupportingDoc(files[i]);
          }
        }
      });

    // Drag and drop functionality for the dropzone
    $('#step12_supporting_docs_zone')
      .off('dragover dragleave drop click')
      .on('dragover', function (e) {
        e.preventDefault();
        e.stopPropagation();
        e.originalEvent.dataTransfer.dropEffect = 'copy';
        $(this).addClass('drag-over');
      })
      .on('dragleave', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('drag-over');
      })
      .on('drop', function (e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('drag-over');
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            processSupportingDoc(files[i]);
          }
        }
      })
      .on('click', function () {
        $('#step12_supporting_docs_upload')[0].click();
      });

    // Remove supporting doc
    $(document)
      .off('click', '.remove-supporting-doc')
      .on('click', '.remove-supporting-doc', function () {
        const index = $(this).data('index');
        removeSupportingDoc(index);
      });
  }

  async function processSupportingDoc(file) {
    try {
      // Validate file type
      const allowedTypes = [
        'application/pdf',
        'application/msword'
      ];

      if (!allowedTypes.includes(file.type)) {
        showMessage('Please upload PDF, DOC, or DOCX files only', 'error');
        return;
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        showMessage('File size must be less than 10MB', 'error');
        return;
      }

      const fileData = await fileToBase64(file);
      const supportingDoc = {
        name: file.name,
        type: file.type,
        size: file.size,
        data: fileData,
      };

      formState.data.supportingDocs.push(supportingDoc);
      updateSupportingDocsList();

      // Clear the file input
      $('#step12_supporting_docs_upload').val('');
    } catch (error) {
      console.error('Error processing supporting document:', error);
      showMessage('Error processing supporting document', 'error');
    }
  }

  function removeSupportingDoc(index) {
    formState.data.supportingDocs.splice(index, 1);
    updateSupportingDocsList();
  }

  function updateSupportingDocsList() {
    const $list = $('#step12_supporting_docs_list');
    $list.empty();

    if (formState.data.supportingDocs.length > 0) {
      // Show the info container
      $('#step12_supporting_docs_info').removeClass('d-none');

      formState.data.supportingDocs.forEach((doc, index) => {
        const $item = $(
          `<div class="supporting-doc-item d-flex align-items-center mb-2">
            <i class="fa fa-file me-2"></i>
            <span class="flex-grow-1">${doc.name}</span>
            <button type="button" class="btn btn-sm btn-outline-danger remove-supporting-doc" data-index="${index}">
              <i class="fa fa-times"></i>
            </button>
          </div>`
        );
        $list.append($item);
      });
    } else {
      // Hide the info container if no files
      $('#step12_supporting_docs_info').addClass('d-none');
    }
  }

  // =============================================================================
  // FORM SUBMISSION
  // =============================================================================

  async function submitStep1() {
    try {
      const fileData = await fileToBase64(formState.data.selectedFile);

      const payload = {
        recommendation_text: formState.data.recommendation,
        file_data: fileData,
        file_name: formState.data.fileName,
        output_result_count: formState.data.outputCount,
      };

      const response = await fetch('/recommendation_mapping/api/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
      });

      const { result } = await response.json();

      if (result.success) {
        formState.recordId = result.record_id;

        if (result.status === 'running') {
          showMessage('Processing in progress...');
          pollForResults(result.record_id);
        }
        return result;
      } else {
        throw new Error(result.message || 'Failed to process recommendations');
      }
    } catch (error) {
      showMessage(
        'Error processing recommendations: ' + error.message,
        'error'
      );
      return false;
    }
  }

  async function submitStep(stepNumber) {
    if (!validateStep(stepNumber)) return false;

    try {
      if (stepNumber === 1) {
        $('#next-btn').text('Processing...').prop('disabled', true);
        const result = await submitStep1();

        $('#next-btn').off('click').on('click', nextStep);
        return result;
      } else if (stepNumber === 12) {
        // Final step submission
        $('#next-btn').text('Submitting...').prop('disabled', true);

        const inputData = collectStepData(stepNumber);

        // Submit final data to server
        try {
          const response = await fetch(
            '/recommendation_mapping/api/final-submission',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
              },
              body: JSON.stringify({
                record_id: formState.recordId,
                final_data: inputData,
              }),
            }
          );

          const {result} = await response.json();

          if (result.success) {
            showMessage(
              'Recommendation plan submitted successfully!',
              'success'
            );
            localStorage.removeItem('record_id');
            setTimeout(() => {
              window.location.href = `/recommendation_mapping/result/${formState.recordId}`;
            }, 2000);
            return true;
          } else {
            throw new Error(result.message || 'Failed to submit final data');
          }
        } catch (error) {
          showMessage('Error submitting final data: ' + error.message, 'error');
          $('#next-btn').prop('disabled', false).text('Submit');
          return false;
        }
      }

      // For other steps, use suggestions handler
      const config = STEP_CONFIG[stepNumber];
      const inputData = collectStepData(stepNumber);

      if (window.CEP_RMT.SuggestionsHandler) {
        return await window.CEP_RMT.SuggestionsHandler.submitStepData(
          stepNumber,
          config.name,
          inputData
        );
      }

      return true;
    } catch (error) {
      console.error(`Step ${stepNumber} submission error:`, error);
      return false;
    }
  }

  function pollForResults(recordId) {
    const pollInterval = setInterval(async () => {
      const result = await fetchSuggestions(recordId);
      if (!result) {
        clearInterval(pollInterval);
        console.log('Polling stopped: result is null');
        return;
      }
      
      if (result.success && result.status === 'completed') {
        localStorage.setItem('record_id', result.record_id);
        resultsState = result.data;
        formState.recordId = result.record_id;
        clearInterval(pollInterval);
        $('#next-btn').prop('disabled', false).text('Next');
        setTimeout(() => {
          nextStep();
        }, 1000);
        
        showMessage('Processing completed successfully!');
      } else if (result.status === 'failed') {
        clearInterval(pollInterval);
        handleFailedResult(result);
      }
    }, 5000);
  }

  async function fetchSuggestions(recordId) {
    try {
      const response = await fetch(
        `/recommendation_mapping/api/suggestions/${recordId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
          },
        }
      );
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Polling error:', error);
      return null;
    }
  }

  function handleFailedResult(result) {
    showMessage(
      'Processing failed: ' + (result.data?.error_message || 'Unknown error'),
      'error'
    );
  }
  // =============================================================================
  // NAVIGATION FUNCTIONS
  // =============================================================================

  async function nextStep() {
    // Validate and submit current step if it has configuration
    if (STEP_CONFIG[formState.currentStep]) {
      if (formState.currentStep >= 2 && formState.currentStep <= 12) {
        const result = await submitStep(formState.currentStep);
        if (!result) return; // Don't proceed if submission failed
      }
    }

    // Move to next step
    if (formState.currentStep < formState.totalSteps) {
      formState.currentStep++;
      updateDisplay();
    }
  }

  function previousStep() {
    if (formState.currentStep > 1) {
      formState.currentStep--;
      updateDisplay();
    }
  }

  function updateDisplay() {
    $('.form-step').addClass('d-none');
    $('#step-' + formState.currentStep).removeClass('d-none');
  

    // check with data-step
    $('.step-line').each(function (index) {
      const stepNumber = index + 1;
      $(this)
        .toggleClass('step-line-active', stepNumber <= formState.currentStep)
        .toggleClass('step-line-inactive', stepNumber > formState.currentStep);
    });

    // Update navigation buttons
    $('#prev-btn').toggleClass('d-none', formState.currentStep === 1);
    $('#next-btn').text(
      formState.currentStep === formState.totalSteps ? 'Submit' : 'Next'
    );

    initStep(formState.currentStep);
  }

  // =============================================================================
  // MAIN INITIALIZATION
  // =============================================================================

  async function initialize() {
    const $container = $('#recommendation-form-container');
    if ($container.length === 0) return;

    formState.data.outputCount = parseInt($('#step1_output_count').val()) || 3;
    const recordId = localStorage.getItem('record_id');
    if (recordId) {
      const result = await fetchSuggestions(recordId);
      if (result.success && result.status === 'completed') {
        formState.currentStep = result.step;
        resultsState = result.data;
        formState.recordId = result.record_id;
      }
    }

    if (formState.currentStep === 1) {
      $('#next-btn')
        .off('click')
        .on('click', () => submitStep(formState.currentStep));
    } else {
      $('#next-btn').off('click').on('click', nextStep);
    }

    $('#prev-btn').off('click').on('click', previousStep);

    $(document)
      .off('click', 'a.text-primary')
      .on('click', 'a.text-primary', function (e) {
        const $link = $(this);
        if ($link.text().trim() === 'Reference from NAP') {
          e.preventDefault();
          if (window.CEP_RMT.SuggestionsHandler) {
            window.CEP_RMT.SuggestionsHandler.showReferencesModal(
              formState.currentStep
            );
          }
        }
      });

    updateDisplay();
  }

  //   // =============================================================================
  //   // PUBLIC API
  //   // =============================================================================

  window.CEP_RMT.FormBase = {
    getFormState: () => formState,
    getResultsState: () => resultsState,
    getApiEndpoint: () => '/recommendation_mapping/api/step-data',
    showSuccessMessage: (message) => showMessage(message, 'success'),
    showErrorMessage: (message) => showMessage(message, 'error'),
    fileToBase64: fileToBase64,
  };

  initialize();
});
