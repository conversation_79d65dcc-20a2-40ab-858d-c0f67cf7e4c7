// Export functionality for Recommendation Mapping Tool Results
$(document).ready(function () {
  'use strict';

  // Initialize export functionality
  function initExportHandlers() {
    // Handle export button clicks
    $('.export-btn').off('click').on('click', function (e) {
      e.preventDefault();

      const format = $(this).data('format');
      const recordId = $('#recommendation_result').data('record-id');

      if (!recordId) {
        showMessage('Error: Record ID not found', 'error');
        return;
      }

      // Check if PDF export is available
      if (format === 'pdf' && typeof html2pdf === 'undefined') {
        showMessage('PDF export not available. HTML2PDF library not loaded.', 'error');
        return;
      }

      exportData(format, recordId);
    });
  }

  // Export data in specified format
  function exportData(format, recordId) {
    // Show loading state
    const $exportBtn = $(`.export-btn[data-format="${format}"]`);
    const originalText = $exportBtn.html();
    $exportBtn.html('<i class="fa fa-spinner fa-spin me-2"></i>Exporting...');
    $exportBtn.prop('disabled', true);

    if (format === 'pdf') {
      // Handle PDF export using HTML2PDF.js
      exportToPDF(recordId)
        .then(() => {
          showMessage('PDF export completed successfully!', 'success');
        })
        .catch(error => {
          console.error('PDF export error:', error);
          showMessage('PDF export failed: ' + error.message, 'error');
        })
        .finally(() => {
          // Restore button state
          $exportBtn.html(originalText);
          $exportBtn.prop('disabled', false);
        });
    } else if (format === 'json') {
      // Handle JSON export
      const exportUrl = `/recommendation_mapping/export/${format}/${recordId}`;

      fetch(exportUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Export failed');
        }
        return response.json();
      })
      .then(data => {
        // Create and download JSON file
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `recommendation_mapping_${recordId}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showMessage('JSON export completed successfully!', 'success');
      })
      .catch(error => {
        console.error('Export error:', error);
        showMessage('Export failed: ' + error.message, 'error');
      })
      .finally(() => {
        // Restore button state
        $exportBtn.html(originalText);
        $exportBtn.prop('disabled', false);
      });
    }
  }

  // Export to PDF using HTML2PDF.js
  function exportToPDF(recordId) {
    return new Promise((resolve, reject) => {
      try {
        // Check if html2pdf is available
        if (typeof html2pdf === 'undefined') {
          reject(new Error('HTML2PDF library not loaded'));
          return;
        }

        // First, try a simple direct approach
        console.log('=== Attempting Simple PDF Export ===');
        const simpleElement = document.querySelector('#recommendation_result .recommendation-results');
        if (simpleElement && simpleElement.innerHTML.length > 100) {
          console.log('Found recommendation-results element, trying direct export');
          return exportSimplePDF(simpleElement, recordId, resolve, reject);
        }

        // Get the element to convert
        const element = document.getElementById('recommendation_result');
        if (!element) {
          reject(new Error('Recommendation result element not found'));
          return;
        }

        // More detailed debugging
        console.log('=== PDF Export Debug Info ===');
        console.log('Element found:', !!element);
        console.log('Element innerHTML length:', element.innerHTML.length);
        console.log('Element children count:', element.children.length);
        console.log('Element classes:', element.className);
        console.log('Element first 200 chars:', element.innerHTML.substring(0, 200));

        // Check for table specifically
        const tables = element.querySelectorAll('table');
        console.log('Tables found:', tables.length);
        if (tables.length > 0) {
          console.log('First table rows:', tables[0].querySelectorAll('tr').length);
          console.log('First table HTML length:', tables[0].innerHTML.length);
        }

        // Check if element has meaningful content
        const textContent = element.textContent || element.innerText || '';
        console.log('Text content length:', textContent.length);

        if (textContent.trim().length < 20) {
          reject(new Error('Recommendation result element appears to have no meaningful content'));
          return;
        }

        // Try different approaches to get content
        let elementToUse;

        // Approach 1: Clone the element
        const clonedElement = element.cloneNode(true);
        console.log('Cloned element HTML length:', clonedElement.innerHTML.length);

        if (clonedElement.innerHTML.length > 100) {
          elementToUse = clonedElement;
          console.log('Using cloned element approach');
        } else {
          // Approach 2: Create a new element with the original's innerHTML
          elementToUse = document.createElement('div');
          elementToUse.innerHTML = element.innerHTML;
          console.log('Using innerHTML copy approach, length:', elementToUse.innerHTML.length);

          if (elementToUse.innerHTML.length < 100) {
            // Approach 3: Create a simple structure with text content
            elementToUse = document.createElement('div');
            const textContent = element.textContent || element.innerText || '';
            elementToUse.innerHTML = `
              <div class="recommendation-results">
                <h2>Recommendation Mapping Results</h2>
                <div style="white-space: pre-wrap; font-family: Arial, sans-serif; font-size: 12px;">
                  ${textContent}
                </div>
              </div>
            `;
            console.log('Using text content fallback approach');
          }
        }

        // Ensure the element maintains its structure
        elementToUse.style.display = 'block';
        elementToUse.style.visibility = 'visible';
        elementToUse.style.opacity = '1';
        elementToUse.style.position = 'static';

        // Add a title to the PDF
        const title = document.createElement('h1');
        title.textContent = 'Recommendation Mapping Tool - Export';
        title.style.cssText = 'text-align: center; color: #249AFB; margin-bottom: 15px; font-size: 16px; page-break-after: avoid;';
        elementToUse.insertBefore(title, elementToUse.firstChild);

        // Only preprocess if we have a proper cloned element
        if (elementToUse === clonedElement) {
          preprocessForPDF(elementToUse);
        }

        // Add some styling for better PDF output
        const style = document.createElement('style');
        style.textContent = `
          .pdf-export {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.3;
            color: #333;
            width: 100%;
            max-width: 210mm;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
          }
          .pdf-export * {
            box-sizing: border-box;
          }
          .pdf-export .table-responsive {
            overflow: visible !important;
            width: 100%;
          }
          .pdf-export .table {
            width: 100% !important;
            border-collapse: collapse;
            margin-bottom: 10px;
            table-layout: fixed;
            font-size: 9px;
          }
          .pdf-export .table th,
          .pdf-export .table td {
            border: 1px solid #ddd;
            padding: 4px 6px;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
          }
          .pdf-export .table th {
            background-color: #249AFB !important;
            color: white !important;
            font-weight: bold;
            font-size: 10px;
            width: 25%;
          }
          .pdf-export .table td.fw-bold {
            background-color: #f8f9fa !important;
            font-weight: bold;
            width: 25%;
            font-size: 9px;
          }
          .pdf-export .table td:not(.fw-bold) {
            width: 75%;
            font-size: 8px;
          }
          .pdf-export .table tr {
            page-break-inside: avoid;
          }
          .pdf-export a {
            color: #007bff !important;
            text-decoration: underline;
            font-size: 8px;
          }
          .pdf-export .text-primary {
            color: #007bff !important;
          }
          .pdf-export .text-muted {
            color: #6c757d !important;
          }
          .pdf-export .fa {
            display: none;
          }
          .pdf-export .btn {
            display: none;
          }
          .pdf-export .dropdown {
            display: none;
          }
          .pdf-export p {
            margin: 2px 0;
            font-size: 8px;
            line-height: 1.2;
          }
          .pdf-export ul, .pdf-export ol {
            margin: 2px 0;
            padding-left: 15px;
          }
          .pdf-export li {
            font-size: 8px;
            line-height: 1.2;
            margin-bottom: 1px;
          }
          .pdf-export .mb-2, .pdf-export .mb-3 {
            margin-bottom: 5px !important;
          }
          .pdf-export .mt-2, .pdf-export .mt-3 {
            margin-top: 5px !important;
          }
          @media print {
            .pdf-export {
              width: 100% !important;
              max-width: none !important;
            }
            .pdf-export .table {
              font-size: 8px !important;
            }
            .pdf-export .table th,
            .pdf-export .table td {
              padding: 3px 4px !important;
            }
          }
        `;

        // elementToUse is already defined above, so we'll use that

        // Create a wrapper div for better control
        const wrapper = document.createElement('div');
        wrapper.style.cssText = 'width: 100%; max-width: 794px; margin: 0 auto; padding: 0;';
        wrapper.classList.add('pdf-export');

        // Add the style to wrapper
        wrapper.appendChild(style);
        wrapper.appendChild(elementToUse);

        console.log('Final wrapper content length:', wrapper.innerHTML.length);

        // Final check - if wrapper still has no meaningful content, try text fallback
        if (wrapper.innerHTML.length < 200) {
          console.warn('Wrapper has insufficient content, trying text fallback');
          return exportTextFallbackPDF(element, recordId, resolve, reject);
        }

        // Configure HTML2PDF options
        const opt = {
          margin: [15, 10, 15, 10],
          filename: `recommendation_mapping_${recordId}.pdf`,
          image: { type: 'jpeg', quality: 0.85 },
          html2canvas: {
            scale: 1.5,
            useCORS: true,
            allowTaint: true,
            scrollX: 0,
            scrollY: 0,
            width: 794, // A4 width in pixels at 96 DPI
            height: null, // Let height be automatic
            dpi: 96,
            letterRendering: true,
            logging: false
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait',
            compress: true,
            precision: 2
          },
          pagebreak: {
            mode: ['css', 'legacy'],
            before: '.page-break-before',
            after: '.page-break-after',
            avoid: ['tr', '.page-break-avoid']
          },
          enableLinks: true
        };

        // Temporarily add wrapper to DOM for better rendering
        wrapper.style.position = 'absolute';
        wrapper.style.left = '-9999px';
        wrapper.style.top = '0';
        document.body.appendChild(wrapper);

        // Generate PDF with timeout
        const timeoutId = setTimeout(() => {
          document.body.removeChild(wrapper);
          reject(new Error('PDF generation timed out after 30 seconds'));
        }, 30000);

        html2pdf()
          .set(opt)
          .from(wrapper)
          .save()
          .then(() => {
            clearTimeout(timeoutId);
            document.body.removeChild(wrapper);
            resolve();
          })
          .catch((error) => {
            clearTimeout(timeoutId);
            if (document.body.contains(wrapper)) {
              document.body.removeChild(wrapper);
            }
            console.error('HTML2PDF Error:', error);
            reject(new Error(`PDF generation failed: ${error.message || 'Unknown error'}`));
          });

      } catch (error) {
        reject(error);
      }
    });
  }

  // Simple PDF export function
  function exportSimplePDF(element, recordId, resolve, reject) {
    console.log('Using simple PDF export approach');

    // Create a simple wrapper
    const wrapper = document.createElement('div');
    wrapper.style.cssText = 'font-family: Arial, sans-serif; font-size: 12px; line-height: 1.4; color: #333; padding: 20px;';

    // Add title
    const title = document.createElement('h1');
    title.textContent = 'Recommendation Mapping Tool - Export';
    title.style.cssText = 'text-align: center; color: #249AFB; margin-bottom: 20px;';
    wrapper.appendChild(title);

    // Clone and add the content
    const content = element.cloneNode(true);
    wrapper.appendChild(content);

    // Simple configuration
    const opt = {
      margin: 20,
      filename: `recommendation_mapping_${recordId}.pdf`,
      image: { type: 'jpeg', quality: 0.8 },
      html2canvas: { scale: 1 },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    // Generate PDF
    html2pdf()
      .set(opt)
      .from(wrapper)
      .save()
      .then(() => {
        console.log('Simple PDF export successful');
        resolve();
      })
      .catch((error) => {
        console.error('Simple PDF export failed:', error);
        reject(error);
      });
  }

  // Text fallback PDF export
  function exportTextFallbackPDF(element, recordId, resolve, reject) {
    console.log('Using text fallback PDF export');

    // Extract all text content
    const textContent = element.textContent || element.innerText || 'No content available';

    // Create a simple HTML structure
    const wrapper = document.createElement('div');
    wrapper.style.cssText = 'font-family: Arial, sans-serif; font-size: 12px; line-height: 1.6; color: #333; padding: 20px; max-width: 700px;';

    wrapper.innerHTML = `
      <h1 style="text-align: center; color: #249AFB; margin-bottom: 30px;">
        Recommendation Mapping Tool - Export
      </h1>
      <div style="white-space: pre-wrap; word-wrap: break-word;">
        ${textContent.replace(/\n\s*\n/g, '\n\n')}
      </div>
      <div style="margin-top: 30px; font-size: 10px; color: #666; text-align: center;">
        Exported on: ${new Date().toLocaleString()}
      </div>
    `;

    // Simple configuration
    const opt = {
      margin: 20,
      filename: `recommendation_mapping_${recordId}.pdf`,
      image: { type: 'jpeg', quality: 0.8 },
      html2canvas: { scale: 1 },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    // Generate PDF
    html2pdf()
      .set(opt)
      .from(wrapper)
      .save()
      .then(() => {
        console.log('Text fallback PDF export successful');
        resolve();
      })
      .catch((error) => {
        console.error('Text fallback PDF export failed:', error);
        reject(error);
      });
  }

  // Preprocess content for better PDF rendering
  function preprocessForPDF(element) {
    console.log('Preprocessing element with', element.children.length, 'children');

    // Remove or hide elements that shouldn't be in PDF (be more specific)
    const elementsToHide = element.querySelectorAll('.btn:not(.export-btn), .dropdown-toggle, .fa-times, .fa-edit, .alert, .modal');
    console.log('Hiding', elementsToHide.length, 'elements');
    elementsToHide.forEach(el => {
      el.style.display = 'none';
      el.style.visibility = 'hidden';
    });

    // Fix table structure for better PDF rendering
    const tables = element.querySelectorAll('table');
    tables.forEach(table => {
      // Ensure table has proper width
      table.style.width = '100%';
      table.style.tableLayout = 'fixed';

      // Process table rows
      const rows = table.querySelectorAll('tr');
      rows.forEach((row) => {
        // Add page break avoidance to table rows
        row.style.pageBreakInside = 'avoid';

        // Adjust cell content for better wrapping
        const cells = row.querySelectorAll('td, th');
        cells.forEach((cell, cellIndex) => {
          // Set appropriate widths
          if (cellIndex === 0) {
            cell.style.width = '25%';
          } else {
            cell.style.width = '75%';
          }

          // Handle long text content
          const textContent = cell.textContent.trim();
          if (textContent.length > 100) {
            cell.style.wordBreak = 'break-word';
            cell.style.overflowWrap = 'break-word';
            cell.style.hyphens = 'auto';
          }

          // Handle lists within cells
          const lists = cell.querySelectorAll('ul, ol');
          lists.forEach(list => {
            list.style.margin = '2px 0';
            list.style.paddingLeft = '15px';
            const items = list.querySelectorAll('li');
            items.forEach(item => {
              item.style.fontSize = '8px';
              item.style.lineHeight = '1.2';
              item.style.marginBottom = '1px';
            });
          });
        });
      });
    });

    // Handle long paragraphs
    const paragraphs = element.querySelectorAll('p');
    paragraphs.forEach(p => {
      if (p.textContent.length > 200) {
        p.style.pageBreakInside = 'avoid';
      }
    });

    // Handle links
    const links = element.querySelectorAll('a');
    links.forEach(link => {
      link.style.wordBreak = 'break-all';
      link.style.fontSize = '8px';
    });
  }

  // Show message function (reuse from form.js if available)
  function showMessage(message, type = 'success') {
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    const alertClass = `alert-${type === 'success' ? 'success' : 'danger'}`;
    const timeout = type === 'success' ? 5000 : 10000;

    const alertHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        <i class="fa ${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;

    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert to the top of the page
    if ($('#show-error-msg').length) {
      $('#show-error-msg').prepend(alertHtml);
    } else {
      $('.container').first().prepend(alertHtml);
    }

    // Auto-hide after timeout
    setTimeout(() => $('.alert').fadeOut(), timeout);
  }

  // Initialize when DOM is ready
  initExportHandlers();
  
  // Re-initialize if content is dynamically loaded (using modern approach)
  if (window.MutationObserver) {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if export buttons were added
          const hasExportBtn = Array.from(mutation.addedNodes).some(node =>
            node.nodeType === 1 && (node.classList?.contains('export-btn') || node.querySelector?.('.export-btn'))
          );
          if (hasExportBtn) {
            setTimeout(initExportHandlers, 100);
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
});
